<?php
require_once('app/Mage.php');
umask(0);
Mage::app('default');

// Kategori bulma fonksiyonu
function getCate($categoryIds) {
    $mainCategory = "";
    $categories = Mage::getResourceModel('catalog/category_collection');

    foreach ($categoryIds as $id) {
        if ($id == 1040) continue;
        $category = $categories->getItemById($id);
        $parents = explode('/', $category->getPath());

        if (count($parents) > 6) $mainCategory = $parents[6];
        elseif (count($parents) > 5) $mainCategory = $parents[5];
        elseif (count($parents) > 4) $mainCategory = $parents[4];
        elseif (count($parents) > 3) $mainCategory = $parents[3];
        elseif (count($parents) > 2) $mainCategory = $parents[2];
        elseif (count($parents) > 1) $mainCategory = $parents[1];
        elseif (count($parents) > 0) $mainCategory = $parents[0];
    }
    return $mainCategory;
}

// Fiyat hesaplama fonksiyonu
function fiyatx($fi, $fiyatyap) {
    $fia = $fi * $fiyatyap;
    return number_format($fia, 2);
}

// İndirim oranı hesaplama
function indirimOrani($orijinalFiyat, $indirimliFiyat) {
    if ($orijinalFiyat == 0) return 0;
    $indirim = $orijinalFiyat - $indirimliFiyat;
    $oran = ($indirim / $orijinalFiyat) * 100;
    return round($oran);
}

// Ürün adını temizleme
function cleanProductName($sku) {
    $name = str_replace("meubelco-", "", strtolower($sku));
    $name = str_replace("meubelco -", "", $name);
    $name = str_replace("meubelco", "", $name);
    $name = str_replace("Vivenla", "", $name);
    $name = strtoupper(ltrim(trim(str_replace("VIVENLA", "", $name)), "-"));
    $name = strtoupper(ltrim(trim(str_replace("Meubelco", "", $name)), "-"));
    $name = strtoupper(ltrim(trim(str_replace("Collezi Bedding", "", $name)), "-"));
    $name = strtoupper(ltrim(trim(str_replace("Collezibedding", "", $name)), "-"));
    $name = strtoupper(ltrim(trim(str_replace("COLLEZIBEDDING", "", $name)), "-"));
    $name = strtoupper(ltrim(trim(str_replace("COLLEZI BEDDING", "", $name)), "-"));
    return $name;
}

// Fiyat çarpanı
$fiyatyap = 1;
if (isset($_GET['fiyatyap'])) {
    $fiyatyap = $_GET['fiyatyap'];
}

// Veritabanı bağlantısı
$resource = Mage::getSingleton('core/resource');
$db_read = $resource->getConnection('core_read');

// Ürünleri çek
$products = array();
$items = $db_read->fetchAll("SELECT * FROM `alles_logs_say` WHERE (`manuf_id` = 33 OR `manuf_id` = 1946) AND `product_id` < 99000 AND `statusx` != 9 AND `sku` NOT LIKE '%Matelas%' ORDER BY sku ASC");

foreach ($items as $item) {
    if ($item['collection'] == "") continue;

    $product_o = Mage::getModel('catalog/product')->setStoreId(5)->load($item['product_id']);

    $item['title'] = $product_o->getMeubelcoFr();
    $item["photo"] = str_replace("www.vivenla.com", "app.meubelco.be", $item["photo"]);
    $item["special_price"] = $product_o->getSpecialPrice();
    $item["price"] = $product_o->getPrice();

    // Promo kontrolü
    if (isset($_GET['promos']) && $item['special_price'] == "") continue;
    if (isset($_GET['promos']) && $item['total'] <= 0) continue;
    if (isset($_GET['stock']) && $item['total'] <= 0) continue;

    // Placeholder kontrol
    if (strpos($item["photo"], "placeholder") !== false) {
        $item["photo"] = "https://app.meubelco.be/media/catalog/product/placeholder/websites/2/placeholder_meu.jpg";
    }

    // Renk bilgisi
    $renkparca = explode(",", $product_o->getColors());
    $renkoy = "";
    foreach ($renkparca as $value) {
        if ($value == "") continue;
        $itemval = $db_read->fetchOne("SELECT value FROM `eav_attribute_option_value` WHERE `store_id` = 5 AND `option_id` = " . $value . " ORDER BY `value_id` DESC");
        if ($renkoy != "") $renkoy = $renkoy . ", " . $itemval;
        else $renkoy = $itemval;
    }

    // Kategori bilgisi
    $categoryIds = $product_o->getCategoryIds();
    $mainCategory = getCate($categoryIds);
    $kname = "OTHERS";
    if ($mainCategory != "") {
        $kname = Mage::getModel("catalog/category")->setStoreId(5)->load($mainCategory)->getName();
    }

    if ($item['total']<=0) continue;

    // Ürün verilerini hazırla
    $product_name = cleanProductName($item['sku']);
    $photo_url = $item["photo"];
    $price = fiyatx($item['pachat'], $fiyatyap);
    $promo_price = fiyatx($item['special_price'], $fiyatyap);
    $discount_percentage = indirimOrani($price, $promo_price);

    // Ürünü diziye ekle
    $products[] = array(
        'name' => $product_name,
        'photo' => $photo_url,
        'price' => $price,
        'promo_price' => $promo_price,
        'discount' => $discount_percentage,
        'dimensions' => '150x50x66 cm', // Varsayılan ölçü, gerçek veriden alınabilir
        'sku' => $item['sku']
    );
}


usort($products, function($a, $b) {
    return strcmp($a['sku'], $b['sku']);
});

// Header ve Footer HTML fonksiyonları
function renderHeader() {
    return '
    <div class="header">
        <div class="logo-container">
            <img src="logo.png" alt="MEUBELCO" class="logo-image">
        </div>

        <div class="promotion-badge">
            <img src="promo.png" alt="PROMOTION" class="promo-image">
        </div>

        <div class="validity-badge">
            VALABLE DU<br>
            15/06 AU<br>
            30/06/2025
        </div>
    </div>';
}

function renderFooter() {
    return '
    <div class="footer">
        TOUS LES PRIX INDIQUÉS DANS CE FOLDER PROMOTIONNEL SONT HORS TVA
        <div class="footer-website">WWW.MEUBELCO.BE</div>
    </div>';
}

// Ürünleri 12'şer gruplara böl
$productChunks = array_chunk($products, 12);

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MEUBELCO - Promotion</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-image: url('background.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            background-repeat: no-repeat;
            background-color: #e8e8e8;
            font-size: 12px;
        }

        .page {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            background: rgba(232, 232, 232, 0.95);
            padding: 20px;
            padding-top: 120px;
            padding-bottom: 100px;
            position: relative;
            page-break-after: always;
        }

        .page:last-child {
            page-break-after: auto;
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            background: #e8e8e8;
            z-index: 1000;
            padding: 15px 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .logo-container {
            width: 120px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .promotion-badge {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .promo-image {
            max-height: 50px;
            object-fit: contain;
        }

        .validity-badge {
            background-color: #ffd700;
            color: #dc143c;
            padding: 8px 12px;
            font-weight: bold;
            font-size: 10px;
            text-align: center;
            border-radius: 0;
        }

        /* Footer */
        .footer {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: #e8e8e8;
            text-align: center;
            font-size: 12px;
            color: #333;
            font-weight: bold;
            line-height: 1.4;
            padding: 15px 20px;
            box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .footer-website {
            color: #333;
            margin-top: 5px;
            font-size: 12px;
        }

        /* Products Grid */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 15px;
        }

        .product-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid #e0e0e0;
            border-radius: 15px;
            padding: 15px;
            position: relative;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            overflow: hidden;
            height: 280px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #dc143c 0%, #ffd700 50%, #dc143c 100%);
        }

        .product-image {
            width: 100%;
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            margin-bottom: 12px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.15);
            height: 160px;
        }

        .product-photo {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
        }

        .discount-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #dc143c;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.25);
            border: 2px solid #fff;
            z-index: 10;
        }

        .product-info {
            position: absolute;
            bottom: 15px;
            left: 15px;
            right: 15px;
            background: rgba(255, 255, 255, 0.95);
            padding: 12px;
            border-radius: 8px;
            backdrop-filter: blur(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .product-name {
            font-weight: bold;
            font-size: 14px;
            color: #333;
            margin-bottom: 6px;
            line-height: 1.2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
        }

        .product-dimensions {
            font-size: 11px;
            color: #666;
            margin-bottom: 8px;
            font-style: italic;
        }

        .price-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .old-price {
            text-decoration: line-through;
            color: #666;
            font-size: 16px;
            font-weight: bold;
        }

        .new-price {
            color: #dc143c;
            font-size: 18px;
            font-weight: bold;
        }

        /* Print Styles */
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            @page {
                size: A4;
                margin: 0;
            }

            body {
                background: white !important;
                background-image: url('background.jpg') !important;
                background-size: cover !important;
                background-position: center !important;
                background-repeat: no-repeat !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            .page {
                width: 100% !important;
                min-height: 100vh !important;
                margin: 0 !important;
                padding: 80px 20px 60px 20px !important;
                background: rgba(255, 255, 255, 0.9) !important;
                page-break-after: always !important;
                position: relative !important;
            }

            .page:last-child {
                page-break-after: auto !important;
            }

            .header {
                position: absolute !important;
                top: 10px !important;
                left: 20px !important;
                right: 20px !important;
                background: #e8e8e8 !important;
                padding: 10px 15px !important;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1) !important;
                z-index: 1000 !important;
            }

            .footer {
                position: absolute !important;
                bottom: 10px !important;
                left: 20px !important;
                right: 20px !important;
                background: #e8e8e8 !important;
                padding: 10px 15px !important;
                box-shadow: 0 -2px 5px rgba(0,0,0,0.1) !important;
                z-index: 1000 !important;
                font-size: 11px !important;
            }

            .products-grid {
                display: grid !important;
                grid-template-columns: repeat(3, 1fr) !important;
                gap: 12px !important;
            }

            .product-card {
                height: 220px !important;
                background: white !important;
                border: 1px solid #e0e0e0 !important;
                border-radius: 10px !important;
                padding: 12px !important;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
                page-break-inside: avoid !important;
            }

            .product-image {
                height: 130px !important;
            }

            .product-photo {
                object-fit: cover !important;
            }

            .discount-badge {
                width: 40px !important;
                height: 40px !important;
                font-size: 12px !important;
                background: #ffd700 !important;
                color: #dc143c !important;
                border: 2px solid #fff !important;
            }

            .product-info {
                background: rgba(255, 255, 255, 0.95) !important;
                padding: 8px !important;
            }

            .product-name {
                font-size: 12px !important;
                margin-bottom: 4px !important;
            }

            .product-dimensions {
                font-size: 10px !important;
                margin-bottom: 6px !important;
            }

            .old-price {
                font-size: 14px !important;
            }

            .new-price {
                font-size: 16px !important;
            }

            .logo-image, .promo-image {
                max-height: 40px !important;
            }

            .validity-badge {
                background-color: #ffd700 !important;
                color: #dc143c !important;
                font-size: 9px !important;
            }
        }
    </style>
</head>
<body>
    <?php foreach ($productChunks as $chunkIndex => $chunk): ?>
    <div class="page">
        <?php echo renderHeader(); ?>

        <div class="products-grid">
            <?php foreach ($chunk as $product): ?>
            <div class="product-card">
                <div class="product-image">
                    <img src="<?php echo htmlspecialchars($product['photo']); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" class="product-photo">
                </div>
                <?php if ($product['discount'] > 0): ?>
                <div class="discount-badge">-<?php echo $product['discount']; ?>%</div>
                <?php endif; ?>
                <div class="product-info">
                    <div class="product-name"><?php echo htmlspecialchars($product['name']); ?></div>
                    <div class="product-dimensions"><?php echo $product['dimensions']; ?></div>
                    <div class="price-section">
                        <?php if ($product['discount'] > 0): ?>
                        <span class="old-price"><?php echo $product['price']; ?>€</span>
                        <span class="new-price"><?php echo $product['promo_price']; ?>€</span>
                        <?php else: ?>
                        <span class="new-price"><?php echo $product['price']; ?>€</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <?php echo renderFooter(); ?>
    </div>
    <?php endforeach; ?>
</body>
</html>
