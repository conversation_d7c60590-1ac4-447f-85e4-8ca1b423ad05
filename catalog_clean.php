<?php
include 'products_folder.php';

// Ürünleri 12'şer gruplara böl
$productsPerPage = 12;
$productChunks = array_chunk($products, $productsPerPage);

// E<PERSON>er hiç ürün yo<PERSON>, boş sayfa oluşturma
if (empty($productChunks)) {
    $productChunks = [[]]; // En az bir boş sayfa
}


?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MEUBELCO - Promotion</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-image: url('stock/background.jpg');
            background-size: contain;
            background-position: center;
            background-repeat: repeat;
            background-color: #e8e8e8;
            font-size: 12px;
        }

        .page {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            background: rgba(232, 232, 232, 0.95);
            backdrop-filter: blur(5px);
            padding: 20px;
            padding-top: 150px;
            padding-bottom: 100px;
            position: relative;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        /* Sayfa kırılması - sadece son sayfa hariç */
        .page:not(:last-child) {
            page-break-after: always;
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            background: #e8e8e8;
            z-index: 1000;
            padding: 15px 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .logo-container {
            width: 120px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .promotion-badge {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .promo-image {
            max-height: 50px;
            object-fit: contain;
        }

        .validity-badge {
            background-color: #ffd700;
            color: #dc143c;
            padding: 8px 12px;
            font-weight: bold;
            font-size: 10px;
            text-align: center;
            border-radius: 0;
        }

        /* Footer */
        .footer {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: #e8e8e8;
            text-align: center;
            font-size: 12px;
            color: #333;
            font-weight: bold;
            line-height: 1.4;
            padding: 15px 20px;
            box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .footer-website {
            color: #333;
            margin-top: 5px;
            font-size: 12px;
        }

        /* Products Grid */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 15px;
        }

        .product-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid #e0e0e0;
            border-radius: 15px;
            padding: 15px;
            position: relative;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            overflow: hidden;
            height: 280px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #dc143c 0%, #ffd700 50%, #dc143c 100%);
        }

        .product-image {
            width: 100%;
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            margin-bottom: 12px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.15);
            height: 160px;
        }

        .product-photo {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 8px;
        }

        .discount-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #dc143c;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.25);
            border: 2px solid #fff;
            z-index: 10;
        }

        .product-info {
            position: absolute;
            bottom: 15px;
            left: 15px;
            right: 15px;
            background: rgba(255, 255, 255, 0.95);
            padding: 12px;
            border-radius: 8px;
            backdrop-filter: blur(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .product-name {
            font-weight: bold;
            font-size: 14px;
            color: #333;
            margin-bottom: 6px;
            line-height: 1.2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
        }

        .product-dimensions {
            font-size: 11px;
            color: #666;
            margin-bottom: 8px;
            font-style: italic;
        }

        .price-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .old-price {
            text-decoration: line-through;
            color: #666;
            font-size: 16px;
            font-weight: bold;
        }

        .new-price {
            color: #dc143c;
            font-size: 18px;
            font-weight: bold;
        }

        /* Print Styles */
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            @page {
                size: A4;
                margin: 0;
            }

            body {
                background: #e8e8e8 !important;
                background-image: url('stock/background.jpg') !important;
                background-size: cover !important;
                background-position: center !important;
                background-repeat: no-repeat !important;
                background-attachment: fixed !important;
                margin: 0 !important;
                padding: 0 !important;
                font-size: 12px !important;
            }

            .page {
                width: 100% !important;
                min-height: 100vh !important;
                margin: 0 !important;
                padding: 80px 20px 60px 20px !important;
                background: rgba(232, 232, 232, 0.95) !important;
                position: relative !important;
                border-radius: 0 !important;
                box-shadow: none !important;
            }

            /* Sayfa kırılması - sadece son sayfa hariç */
            .page:not(:last-child) {
                page-break-after: always !important;
            }

            .header {
                position: absolute !important;
                top: 10px !important;
                left: 20px !important;
                right: 20px !important;
                background: #e8e8e8 !important;
                padding: 10px 15px !important;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1) !important;
                z-index: 1000 !important;
            }

            .footer {
                position: absolute !important;
                bottom: 10px !important;
                left: 20px !important;
                right: 20px !important;
                background: #e8e8e8 !important;
                padding: 10px 15px !important;
                box-shadow: 0 -2px 5px rgba(0,0,0,0.1) !important;
                z-index: 1000 !important;
                font-size: 11px !important;
            }

            .products-grid {
                display: grid !important;
                grid-template-columns: repeat(3, 1fr) !important;
                gap: 8px !important;
                margin-bottom: 10px !important;
            }

            .product-card {
                height: 200px !important;
                background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%) !important;
                border: 1px solid #e0e0e0 !important;
                border-radius: 10px !important;
                padding: 10px !important;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
                page-break-inside: avoid !important;
                overflow: hidden !important;
            }

            .product-card::before {
                background: linear-gradient(90deg, #dc143c 0%, #ffd700 50%, #dc143c 100%) !important;
                height: 3px !important;
            }

            .product-image {
                height: 110px !important;
                border-radius: 8px !important;
                margin-bottom: 8px !important;
                box-shadow: 0 2px 6px rgba(0,0,0,0.1) !important;
            }

            .product-photo {
                object-fit: contain !important;
            }

            .discount-badge {
                width: 40px !important;
                height: 40px !important;
                font-size: 12px !important;
                background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%) !important;
                color: #dc143c !important;
                border: 2px solid #fff !important;
                border-radius: 50% !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                font-weight: bold !important;
                box-shadow: 0 4px 12px rgba(0,0,0,0.25) !important;
            }

            .product-info {
                background: rgba(255, 255, 255, 0.95) !important;
                backdrop-filter: blur(5px) !important;
                padding: 6px !important;
                border-radius: 6px !important;
                box-shadow: 0 1px 4px rgba(0,0,0,0.1) !important;
            }

            .product-name {
                font-size: 11px !important;
                margin-bottom: 3px !important;
                line-height: 1.2 !important;
            }

            .product-dimensions {
                font-size: 9px !important;
                margin-bottom: 4px !important;
            }

            .old-price {
                font-size: 12px !important;
            }

            .new-price {
                font-size: 14px !important;
            }

            .logo-image, .promo-image {
                max-height: 40px !important;
            }

            .validity-badge {
                background-color: #ffd700 !important;
                color: #dc143c !important;
                font-size: 9px !important;
            }
        }
    </style>
</head>
<body>
<?php foreach ($productChunks as $pageIndex => $chunk): ?>
    <div class="page">
        <!-- Header -->
        <div class="header">
            <div class="logo-container">
                <img src="stock/logo.png" alt="MEUBELCO" class="logo-image">
            </div>
            <div class="promotion-badge">
                <img src="stock/promo.png" alt="PROMOTION" class="promo-image">
            </div>
            <div class="validity-badge">
                VALABLE DU<br>
                15/06 AU<br>
                30/06/2025
            </div>
        </div>

        <div class="products-grid">
            <?php foreach ($chunk as $product): ?>
                <div class="product-card">
                    <div class="product-image">
                        <img src="<?php echo htmlspecialchars($product['photo']); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" class="product-photo">
                    </div>
                    <?php if ($product['discount'] > 0): ?>
                        <div class="discount-badge">-<?php echo $product['discount']; ?>%</div>
                    <?php endif; ?>
                    <div class="product-info">
                        <div class="product-name"><?php echo htmlspecialchars($product['name']); ?></div>
                        <div class="product-dimensions"><?php echo $product['dimensions']; ?></div>
                        <div class="price-section">
                            <?php if ($product['discount'] > 0): ?>
                                <span class="old-price"><?php echo $product['price']; ?>€</span>
                                <span class="new-price"><?php echo $product['promo_price']; ?>€</span>
                            <?php else: ?>
                                <span class="new-price"><?php echo $product['price']; ?>€</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Footer -->
        <div class="footer">
            TOUS LES PRIX INDIQUÉS DANS CE FOLDER PROMOTIONNEL SONT HORS TVA
            <div class="footer-website">WWW.MEUBELCO.BE</div>
        </div>
    </div>
<?php endforeach; ?>
</body>
</html>
