<?php  
require_once('app/Mage.php');
umask(0);
Mage::app('default');

// Kategori bulma fonksiyonu
function getCate($categoryIds) {
    $mainCategory = "";
    $categories = Mage::getResourceModel('catalog/category_collection');
    
    foreach ($categoryIds as $id) {
        if ($id == 1040) continue;
        $category = $categories->getItemById($id);
        $parents = explode('/', $category->getPath());
        
        if (count($parents) > 6) $mainCategory = $parents[6];
        elseif (count($parents) > 5) $mainCategory = $parents[5];
        elseif (count($parents) > 4) $mainCategory = $parents[4];
        elseif (count($parents) > 3) $mainCategory = $parents[3];
        elseif (count($parents) > 2) $mainCategory = $parents[2];
        elseif (count($parents) > 1) $mainCategory = $parents[1];
        elseif (count($parents) > 0) $mainCategory = $parents[0];
    }
    return $mainCategory;
}

// Fiyat hesaplama fonksiyonu
function fiyatx($fi, $fiyatyap) {
    $fia = $fi * $fiyatyap;
    return number_format($fia, 2);
}

// İndirim oranı hesaplama
function indirimOrani($orijinalFiyat, $indirimliFiyat) {
    if ($orijinalFiyat == 0) return 0;
    $indirim = $orijinalFiyat - $indirimliFiyat;
    $oran = ($indirim / $orijinalFiyat) * 100;
    return round($oran);
}

// Ürün adını temizleme
function cleanProductName($sku) {
    $name = str_replace("meubelco-", "", strtolower($sku));
    $name = str_replace("meubelco -", "", $name);
    $name = str_replace("meubelco", "", $name);
    $name = str_replace("Vivenla", "", $name);
    $name = strtoupper(ltrim(trim(str_replace("VIVENLA", "", $name)), "-"));
    $name = strtoupper(ltrim(trim(str_replace("Meubelco", "", $name)), "-"));
    $name = strtoupper(ltrim(trim(str_replace("Collezi Bedding", "", $name)), "-"));
    $name = strtoupper(ltrim(trim(str_replace("Collezibedding", "", $name)), "-"));
    $name = strtoupper(ltrim(trim(str_replace("COLLEZIBEDDING", "", $name)), "-"));
    $name = strtoupper(ltrim(trim(str_replace("COLLEZI BEDDING", "", $name)), "-"));
    return $name;
}

// Fiyat çarpanı
$fiyatyap = 1;
if (isset($_GET['fiyatyap'])) {
    $fiyatyap = $_GET['fiyatyap'];
}

// Veritabanı bağlantısı
$resource = Mage::getSingleton('core/resource');
$db_read = $resource->getConnection('core_read');

// Ürünleri çek
$products = array();
$items = $db_read->fetchAll("SELECT * FROM `alles_logs_say` WHERE (`manuf_id` = 33 OR `manuf_id` = 1946) AND `product_id` < 99000 AND `statusx` != 9 AND `sku` NOT LIKE '%Matelas%' ORDER BY kname, sku ASC LIMIT 12");

foreach ($items as $item) {
    if ($item['collection'] == "") continue;
    
    $product_o = Mage::getModel('catalog/product')->setStoreId(5)->load($item['product_id']);
    
    $item['title'] = $product_o->getMeubelcoFr();
    $item["photo"] = str_replace("www.vivenla.com", "app.meubelco.be", $item["photo"]);
    $item["special_price"] = $product_o->getSpecialPrice();
    $item["price"] = $product_o->getPrice();
    
    // Promo kontrolü
    if (isset($_GET['promos']) && $item['special_price'] == "") continue;
    if (isset($_GET['promos']) && $item['total'] <= 0) continue;
    if (isset($_GET['stock']) && $item['total'] <= 0) continue;
    
    // Placeholder kontrol
    if (strpos($item["photo"], "placeholder") !== false) {
        $item["photo"] = "https://app.meubelco.be/media/catalog/product/placeholder/websites/2/placeholder_meu.jpg";
    }
    
    // Renk bilgisi
    $renkparca = explode(",", $product_o->getColors());
    $renkoy = "";
    foreach ($renkparca as $value) {
        if ($value == "") continue;
        $itemval = $db_read->fetchOne("SELECT value FROM `eav_attribute_option_value` WHERE `store_id` = 5 AND `option_id` = " . $value . " ORDER BY `value_id` DESC");
        if ($renkoy != "") $renkoy = $renkoy . ", " . $itemval;
        else $renkoy = $itemval;
    }
    
    // Kategori bilgisi
    $categoryIds = $product_o->getCategoryIds();
    $mainCategory = getCate($categoryIds);
    $kname = "OTHERS";
    if ($mainCategory != "") {
        $kname = Mage::getModel("catalog/category")->setStoreId(5)->load($mainCategory)->getName();
    }
    
    // Ürün verilerini hazırla
    $product_name = cleanProductName($item['sku']);
    $photo_url = $item["photo"];
    $price = fiyatx($item['pachat'], $fiyatyap);
    $promo_price = fiyatx($item['special_price'], $fiyatyap);
    $discount_percentage = indirimOrani($price, $promo_price);
    
    // Ürünü diziye ekle
    $products[] = array(
        'name' => $product_name,
        'photo' => $photo_url,
        'price' => $price,
        'promo_price' => $promo_price,
        'discount' => $discount_percentage,
        'dimensions' => '150x50x66 cm', // Varsayılan ölçü, gerçek veriden alınabilir
        'sku' => $item['sku']
    );
}

// Eğer 12'den az ürün varsa, varsayılan ürünlerle doldur
while (count($products) < 12) {
    $products[] = array(
        'name' => 'SAMPLE PRODUCT',
        'photo' => 'product.jpg',
        'price' => '109.00',
        'promo_price' => '93.00',
        'discount' => 15,
        'dimensions' => '150x50x66 cm',
        'sku' => 'SAMPLE-SKU'
    );
}

?>
