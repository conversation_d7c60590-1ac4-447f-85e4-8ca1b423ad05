<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MEUBELCO - Promotion</title>
    <script>
        // PHP kodundan adapte edilen JavaScript fonksiyonları

        // Fiyat hesaplama fonksiyonu (PHP'deki fiyatx fonksiyonu)
        function calculatePrice(basePrice, multiplier = 1) {
            const calculatedPrice = basePrice * multiplier;
            return calculatedPrice.toFixed(2);
        }

        // İndirim oranı hesaplama (PHP'deki indirimOrani fonksiyonu)
        function calculateDiscountPercentage(originalPrice, discountedPrice) {
            if (originalPrice == 0) return 0;
            const discount = originalPrice - discountedPrice;
            const percentage = (discount / originalPrice) * 100;
            return Math.round(percentage);
        }

        // Ürün adını temizleme (PHP'deki name temizleme mantığı)
        function cleanProductName(sku) {
            let name = sku.toLowerCase();
            name = name.replace(/meubelco-?/g, '');
            name = name.replace(/vivenla/g, '');
            name = name.replace(/collezi bedding/g, '');
            name = name.replace(/collezibedding/g, '');
            name = name.trim().replace(/^-+/, '');
            return name.toUpperCase();
        }

        // Dinamik ürün verilerini yükleme
        function loadDynamicProducts() {
            // Bu fonksiyon gerçek API'den veri çekebilir
            // Şimdilik örnek veri kullanıyoruz
            const sampleProducts = [
                {
                    sku: 'MEUBELCO-ELENA-ST-3505-BL',
                    photo: 'product.jpg',
                    price: 129,
                    special_price: 103,
                    dimensions: '148x47x64 cm'
                },
                {
                    sku: 'MEUBELCO-MARCO-ST-3506-WH',
                    photo: 'product.jpg',
                    price: 119,
                    special_price: 98,
                    dimensions: '152x49x65 cm'
                },
                {
                    sku: 'MEUBELCO-SOFIA-ST-3507-GR',
                    photo: 'product.jpg',
                    price: 99,
                    special_price: 84,
                    dimensions: '146x46x63 cm'
                }
            ];

            return sampleProducts;
        }

        // Sayfa yüklendiğinde dinamik ürünleri güncelle
        document.addEventListener('DOMContentLoaded', function() {
            const products = loadDynamicProducts();
            const productCards = document.querySelectorAll('.products-grid .product-card');

            // Son 3 ürünü (4. satır) güncelle
            const startIndex = productCards.length - 3;

            products.forEach((product, index) => {
                if (startIndex + index < productCards.length) {
                    const card = productCards[startIndex + index];
                    updateProductCard(card, product);
                }
            });
        });

        // Ürün kartını güncelleme fonksiyonu
        function updateProductCard(card, productData) {
            const cleanName = cleanProductName(productData.sku);
            const discountPercentage = calculateDiscountPercentage(productData.price, productData.special_price);

            // Ürün resmini güncelle
            const img = card.querySelector('.product-photo');
            if (img) {
                img.src = productData.photo;
                img.alt = cleanName;
            }

            // İndirim yüzdesini güncelle
            const discountBadge = card.querySelector('.discount-badge');
            if (discountBadge) {
                discountBadge.textContent = `-${discountPercentage}%`;
            }

            // Ürün adını güncelle
            const productName = card.querySelector('.product-name');
            if (productName) {
                productName.textContent = cleanName;
            }

            // Ölçüleri güncelle
            const dimensions = card.querySelector('.product-dimensions');
            if (dimensions) {
                dimensions.textContent = productData.dimensions;
            }

            // Fiyatları güncelle
            const oldPrice = card.querySelector('.old-price');
            const newPrice = card.querySelector('.new-price');
            if (oldPrice) oldPrice.textContent = `${productData.price}€`;
            if (newPrice) newPrice.textContent = `${productData.special_price}€`;
        }
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }


        body {
            font-family: Arial, sans-serif;
            background-image: url('background.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            background-repeat: no-repeat;
            background-color: #e8e8e8;
            padding: 15px;
            font-size: 12px;
        }

        .page {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            background: rgba(232, 232, 232, 0.95);
            padding: 20px;
            padding-top: 80px; /* Header için boşluk */
            padding-bottom: 100px; /* Footer için boşluk */
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #e8e8e8;
            z-index: 1000;
            padding: 5px 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .logo-container {
            width: 120px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .promotion-badge {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .promo-image {
            max-height: 50px;
            object-fit: contain;
        }

        .validity-badge {
            background-color: #ffd700;
            color: #dc143c;
            padding: 8px 12px;
            font-weight: bold;
            font-size: 12px;
            text-align: center;
            border-radius: 0;
        }

        /* Main Layout - Grid for all products */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 15px;
        }

        /* Product Cards */
        .product-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid #e0e0e0;
            border-radius: 15px;
            padding: 15px;
            position: relative;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #dc143c 0%, #ffd700 50%, #dc143c 100%);
        }

        .products-grid .product-card {
            height: 280px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .products-grid .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        /* Product Images */
        .product-image {
            width: 100%;
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            margin-bottom: 12px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.15);
        }

        .products-grid .product-image {
            height: 160px;
        }

        .product-photo {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 8px;
        }

        /* Discount Badge */
        .discount-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #dc143c;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.25);
            border: 2px solid #fff;
            z-index: 10;
        }

        /* Product Info */
        .product-info {
            position: absolute;
            bottom: 15px;
            left: 15px;
            right: 15px;
            background: rgba(255, 255, 255, 0.95);
            padding: 12px;
            border-radius: 8px;
            backdrop-filter: blur(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .product-name {
            font-weight: bold;
            font-size: 14px;
            color: #333;
            margin-bottom: 6px;
            line-height: 1.2;
        }

        .product-dimensions {
            font-size: 11px;
            color: #666;
            margin-bottom: 8px;
            font-style: italic;
        }

        .price-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .old-price {
            text-decoration: line-through;
            color: #666;
            font-size: 16px;
            font-weight: bold;
        }

        .new-price {
            color: #dc143c;
            font-size: 18px;
            font-weight: bold;
        }



        /* Footer */
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #e8e8e8;
            text-align: center;
            font-size: 12px;
            color: #333;
            font-weight: bold;
            line-height: 1;
            padding: 5px 20px;
            box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .footer-website {
            color: #333;
            margin-top: 5px;
            font-size: 12px;
        }

        /* Print Styles - Optimized for Chrome PDF */
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            @page {
                size: A4;
                margin: 1.2in 0.5in 1in 0.5in;
            }

            body {
                padding: 0 !important;
                margin: 0 !important;
                background: white !important;
                background-image: url('background.jpg') !important;
                background-size: cover !important;
                background-position: center !important;
                background-repeat: no-repeat !important;
                font-size: 11px !important;
            }

            .page {
                width: 100% !important;
                max-width: none !important;
                min-height: auto !important;
                margin: 0 !important;
                padding: 80px 20px 60px 20px !important;
                background: rgba(255, 255, 255, 0.9) !important;
                border-radius: 0 !important;
                box-shadow: none !important;
            }

            .header {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                width: 100% !important;
                background: #e8e8e8 !important;
                padding: 5px 20px !important;
                margin: 0 !important;
                z-index: 1000 !important;
                box-shadow: none !important;
                border-bottom: 2px solid #ccc !important;
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                height: 60px !important;
            }

            .footer {
                position: fixed !important;
                bottom: 0 !important;
                left: 0 !important;
                right: 0 !important;
                width: 100% !important;
                background: #e8e8e8 !important;
                padding: 10px 20px !important;
                margin: 0 !important;
                z-index: 1000 !important;
                box-shadow: none !important;
                border-top: 2px solid #ccc !important;
                text-align: center !important;
                font-size: 11px !important;
                color: #333 !important;
                font-weight: bold !important;
                line-height: 1.3 !important;
                height: 50px !important;
            }

            .products-grid {
                display: grid !important;
                grid-template-columns: repeat(3, 1fr) !important;
                gap: 12px !important;
                margin-bottom: 15px !important;
            }

            .product-card {
                background: white !important;
                border: 2px solid #e0e0e0 !important;
                border-radius: 10px !important;
                padding: 12px !important;
                position: relative !important;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
                height: 240px !important;
                page-break-inside: avoid !important;
            }

            .product-card::before {
                background: linear-gradient(90deg, #dc143c 0%, #ffd700 50%, #dc143c 100%) !important;
                height: 3px !important;
            }

            .product-image {
                height: 130px !important;
                border-radius: 8px !important;
                margin-bottom: 10px !important;
                box-shadow: 0 2px 6px rgba(0,0,0,0.1) !important;
            }

            .product-photo {
                width: 100% !important;
                height: 100% !important;
                object-fit: contain !important;
                border-radius: 6px !important;
            }

            .discount-badge {
                background: #ffd700 !important;
                color: #dc143c !important;
                border: 2px solid #dc143c !important;
                width: 40px !important;
                height: 40px !important;
                font-size: 12px !important;
                font-weight: bold !important;
            }

            .product-info {
                background: rgba(255, 255, 255, 0.95) !important;
                padding: 8px !important;
                border-radius: 6px !important;
                box-shadow: 0 1px 4px rgba(0,0,0,0.1) !important;
            }

            .product-name {
                font-size: 12px !important;
                font-weight: bold !important;
                color: #333 !important;
                margin-bottom: 4px !important;
                line-height: 1.2 !important;
            }

            .product-dimensions {
                font-size: 10px !important;
                color: #666 !important;
                margin-bottom: 6px !important;
                font-style: italic !important;
            }

            .old-price {
                font-size: 14px !important;
                color: #666 !important;
                font-weight: bold !important;
            }

            .new-price {
                font-size: 16px !important;
                color: #dc143c !important;
                font-weight: bold !important;
            }

            .promotion-badge {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            .promo-image {
                max-height: 40px !important;
                object-fit: contain !important;
            }

            .validity-badge {
                background-color: #ffd700 !important;
                color: #dc143c !important;
                font-size: 12px !important;
                padding: 6px 10px !important;
            }

            .logo-container {
                width: 100px !important;
                height: 50px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            .logo-image {
                max-width: 100% !important;
                max-height: 100% !important;
                object-fit: contain !important;
            }


        }
    </style>
</head>
<body>
    <div class="page">
        <!-- Header -->
        <div class="header">
            <div class="logo-container">
                <img src="logo.png" alt="MEUBELCO" class="logo-image">
            </div>

            <div class="promotion-badge">
                <img src="promo.png" alt="PROMOTION" class="promo-image">
            </div>

            <div class="validity-badge">
                VALABLE DU<br>
                15/06 AU<br>
                30/06/2025
            </div>
        </div>

        <!-- Products Grid - All products in rows of 3 -->
        <div class="products-grid">
            <!-- First Row -->
            <div class="product-card">
                <div class="product-image">
                    <img src="product.jpg" alt="PABLO - ST-3504-GR" class="product-photo">
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">PABLO - ST-3504-GR</div>
                    <div class="product-dimensions">150x50x66 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <div class="product-card">
                <div class="product-image">
                    <img src="product.jpg" alt="KATERINA - ST-3503-GR" class="product-photo">
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">KATERINA - ST-3503-GR</div>
                    <div class="product-dimensions">145x48x62 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <div class="product-card">
                <div class="product-image">
                    <img src="product.jpg" alt="KATERINA - ST-3503-WH" class="product-photo">
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">KATERINA - ST-3503-WH</div>
                    <div class="product-dimensions">145x48x62 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <!-- Second Row -->
            <div class="product-card">
                <div class="product-image">
                    <img src="product.jpg" alt="PABLO - ST-3504-WH" class="product-photo">
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">PABLO - ST-3504-WH</div>
                    <div class="product-dimensions">150x50x66 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <div class="product-card">
                <div class="product-image">
                    <img src="product.jpg" alt="PABLO - ST-3504-GR" class="product-photo">
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">PABLO - ST-3504-GR</div>
                    <div class="product-dimensions">150x50x66 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <div class="product-card">
                <div class="product-image">
                    <img src="product.jpg" alt="SAVONA - ST-4826" class="product-photo">
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">SAVONA - ST-4826</div>
                    <div class="product-dimensions">160x55x70 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <!-- Third Row -->
            <div class="product-card">
                <div class="product-image">
                    <img src="product.jpg" alt="MARTA - ST-3502-WH" class="product-photo">
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">MARTA - ST-3502-WH</div>
                    <div class="product-dimensions">140x45x60 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <div class="product-card">
                <div class="product-image">
                    <img src="product.jpg" alt="MARTA - ST-3502-GR" class="product-photo">
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">MARTA - ST-3502-GR</div>
                    <div class="product-dimensions">140x45x60 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <div class="product-card">
                <div class="product-image">
                    <img src="product.jpg" alt="SAVONA - ST-4827" class="product-photo">
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">SAVONA - ST-4827</div>
                    <div class="product-dimensions">155x52x68 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <!-- Fourth Row - Dynamic Products -->
            <div class="product-card">
                <div class="product-image">
                    <img src="product.jpg" alt="Dynamic Product 1" class="product-photo">
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">DYNAMIC PRODUCT 1</div>
                    <div class="product-dimensions">150x50x66 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <div class="product-card">
                <div class="product-image">
                    <img src="product.jpg" alt="Dynamic Product 2" class="product-photo">
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">DYNAMIC PRODUCT 2</div>
                    <div class="product-dimensions">150x50x66 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <div class="product-card">
                <div class="product-image">
                    <img src="product.jpg" alt="Dynamic Product 3" class="product-photo">
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">DYNAMIC PRODUCT 3</div>
                    <div class="product-dimensions">150x50x66 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            TOUS LES PRIX INDIQUÉS DANS CE FOLDER PROMOTIONNEL SONT HORS TVA
            <div class="footer-website">WWW.MEUBELCO.BE</div>
        </div>
    </div>
</body>
</html>