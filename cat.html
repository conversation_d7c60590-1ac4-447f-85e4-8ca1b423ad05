<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MEUBELCO - Promotion</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }


        body {
            font-family: Arial, sans-serif;
            background-color: #e8e8e8;
            margin: 0;
            padding: 0;
            font-size: 12px;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('background.jpg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            z-index: -1;
        }

        .page {
            width: 210mm;
            min-height: calc(100vh - 200px); /* Header ve footer bo<PERSON> */
            margin: 0 auto;
            background: rgba(232, 232, 232, 0.95); /* Semi-transparent background */
            backdrop-filter: blur(5px);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
            z-index: 1;
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(232, 232, 232, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 15px 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .logo-container {
            width: 120px;
            height: 60px;
            background-color: #2a2a2a;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-inner {
            position: relative;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }

        .logo-squares {
            position: absolute;
            top: 8px;
            left: 8px;
            width: 25px;
            height: 25px;
        }

        .yellow-square {
            width: 15px;
            height: 15px;
            background-color: #ffd700;
            position: absolute;
            top: 0;
            left: 0;
        }

        .white-square {
            width: 12px;
            height: 12px;
            background-color: white;
            position: absolute;
            top: 8px;
            left: 8px;
            border: 2px solid #2a2a2a;
        }

        .promotion-badge {
            background-color: #dc143c;
            color: white;
            padding: 12px 25px;
            font-weight: bold;
            font-size: 24px;
            border: 3px solid #dc143c;
            letter-spacing: 1px;
        }

        .validity-badge {
            background-color: #ffd700;
            color: #dc143c;
            padding: 8px 12px;
            font-weight: bold;
            font-size: 10px;
            text-align: center;
            border-radius: 0;
        }

        /* Main Layout - Grid for all products */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 15px;
        }

        /* Product Cards */
        .product-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid #e0e0e0;
            border-radius: 15px;
            padding: 15px;
            position: relative;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #dc143c 0%, #ffd700 50%, #dc143c 100%);
        }

        .products-grid .product-card {
            height: 280px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .products-grid .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        /* Product Images - Hill Landscape */
        .product-image {
            width: 100%;
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            margin-bottom: 12px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.15);
        }

        .products-grid .product-image {
            height: 160px;
        }

        .landscape {
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, 
                #87ceeb 0%, 
                #87ceeb 45%, 
                #c8e6c9 45%, 
                #c8e6c9 65%, 
                #8bc34a 65%, 
                #8bc34a 85%, 
                #689f38 85%
            );
            position: relative;
        }

        /* Clouds */
        .cloud {
            position: absolute;
            background: white;
            border-radius: 25px;
            opacity: 0.9;
        }

        .cloud1 {
            width: 35px;
            height: 18px;
            top: 15px;
            left: 25px;
        }

        .cloud1::before {
            content: '';
            position: absolute;
            background: white;
            border-radius: 20px;
            width: 20px;
            height: 20px;
            top: -8px;
            left: 5px;
        }

        .cloud1::after {
            content: '';
            position: absolute;
            background: white;
            border-radius: 15px;
            width: 15px;
            height: 15px;
            top: -5px;
            left: 20px;
        }

        .cloud2 {
            width: 25px;
            height: 12px;
            top: 25px;
            right: 30px;
        }

        .cloud2::before {
            content: '';
            position: absolute;
            background: white;
            border-radius: 15px;
            width: 15px;
            height: 15px;
            top: -6px;
            left: 3px;
        }

        /* Discount Badge */
        .discount-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #dc143c;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.25);
            border: 2px solid #fff;
            z-index: 10;
        }

        /* Product Info */
        .product-info {
            position: absolute;
            bottom: 15px;
            left: 15px;
            right: 15px;
            background: rgba(255, 255, 255, 0.95);
            padding: 12px;
            border-radius: 8px;
            backdrop-filter: blur(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .product-name {
            font-weight: bold;
            font-size: 14px;
            color: #333;
            margin-bottom: 6px;
            line-height: 1.2;
        }

        .product-dimensions {
            font-size: 11px;
            color: #666;
            margin-bottom: 8px;
            font-style: italic;
        }

        .price-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .old-price {
            text-decoration: line-through;
            color: #666;
            font-size: 16px;
            font-weight: bold;
        }

        .new-price {
            color: #dc143c;
            font-size: 18px;
            font-weight: bold;
        }



        /* Footer */
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(232, 232, 232, 0.95);
            backdrop-filter: blur(10px);
            text-align: center;
            font-size: 12px;
            color: #333;
            font-weight: bold;
            line-height: 1.4;
            padding: 15px 20px;
            box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .footer-website {
            color: #333;
            margin-top: 5px;
            font-size: 12px;
        }

        /* Print Styles */
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            body {
                padding: 0;
                padding-top: 120px;
                padding-bottom: 100px;
                background: #e8e8e8 !important;
                background-image: url('background.jpg') !important;
                background-size: cover !important;
                background-position: center !important;
                background-attachment: fixed !important;
                background-repeat: no-repeat !important;
            }

            .page {
                width: auto;
                min-height: auto;
                margin: 0;
                padding: 20px;
                background: rgba(232, 232, 232, 0.95) !important;
                backdrop-filter: blur(5px);
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;
            }

            .header {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                background: #e8e8e8 !important;
                border-bottom: 1px solid #ccc;
                padding: 15px 20px !important;
                margin: 0;
                z-index: 1000 !important;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1) !important;
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
            }

            .footer {
                position: fixed !important;
                bottom: 0 !important;
                left: 0 !important;
                right: 0 !important;
                background: #e8e8e8 !important;
                border-top: 1px solid #ccc;
                padding: 15px 20px !important;
                margin: 0;
                z-index: 1000 !important;
                box-shadow: 0 -2px 5px rgba(0,0,0,0.1) !important;
                text-align: center !important;
                font-size: 12px !important;
                color: #333 !important;
                font-weight: bold !important;
                line-height: 1.4 !important;
            }

            .product-card {
                background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%) !important;
                border: 1px solid #e0e0e0 !important;
                border-radius: 15px !important;
                box-shadow: 0 4px 15px rgba(0,0,0,0.08) !important;
            }

            .product-card::before {
                background: linear-gradient(90deg, #dc143c 0%, #ffd700 50%, #dc143c 100%) !important;
            }

            .discount-badge {
                background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%) !important;
                color: #dc143c !important;
                border: 2px solid #fff !important;
                box-shadow: 0 4px 12px rgba(0,0,0,0.25) !important;
            }

            .product-info {
                background: rgba(255, 255, 255, 0.95) !important;
                backdrop-filter: blur(5px);
                box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            }

            .landscape {
                background: linear-gradient(to bottom,
                    #87ceeb 0%,
                    #87ceeb 45%,
                    #c8e6c9 45%,
                    #c8e6c9 65%,
                    #8bc34a 65%,
                    #8bc34a 85%,
                    #689f38 85%
                ) !important;
            }

            .promotion-badge {
                background-color: #dc143c !important;
                color: white !important;
                border: 3px solid #dc143c !important;
            }

            .validity-badge {
                background-color: #ffd700 !important;
                color: #dc143c !important;
            }

            .logo-container {
                background-color: #2a2a2a !important;
            }

            .yellow-square {
                background-color: #ffd700 !important;
            }

            .white-square {
                background-color: white !important;
                border: 2px solid #2a2a2a !important;
            }

            /* Her sayfada header ve footer tekrarlanması için */
            @page {
                margin-top: 120px !important;
                margin-bottom: 100px !important;
                size: A4;
            }
        }
    </style>
</head>
<body>
    <div class="page">
        <!-- Header -->
        <div class="header">
            <div class="logo-container">
                <div class="logo-squares">
                    <div class="yellow-square"></div>
                    <div class="white-square"></div>
                </div>
                <div class="logo-inner" style="margin-left: 35px;">MEUBELCO</div>
            </div>
            
            <div class="promotion-badge">PROMOTION</div>
            
            <div class="validity-badge">
                VALABLE DU<br>
                15/06 AU<br>
                30/06/2025
            </div>
        </div>

        <!-- Products Grid - All products in rows of 3 -->
        <div class="products-grid">
            <!-- First Row -->
            <div class="product-card">
                <div class="product-image">
                    <div class="landscape">
                        <div class="cloud cloud1"></div>
                        <div class="cloud cloud2"></div>
                    </div>
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">PABLO - ST-3504-GR</div>
                    <div class="product-dimensions">150x50x66 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <div class="product-card">
                <div class="product-image">
                    <div class="landscape">
                        <div class="cloud cloud1"></div>
                    </div>
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">KATERINA - ST-3503-GR</div>
                    <div class="product-dimensions">145x48x62 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <div class="product-card">
                <div class="product-image">
                    <div class="landscape">
                        <div class="cloud cloud1"></div>
                    </div>
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">KATERINA - ST-3503-WH</div>
                    <div class="product-dimensions">145x48x62 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <!-- Second Row -->
            <div class="product-card">
                <div class="product-image">
                    <div class="landscape">
                        <div class="cloud cloud1"></div>
                    </div>
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">PABLO - ST-3504-WH</div>
                    <div class="product-dimensions">150x50x66 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <div class="product-card">
                <div class="product-image">
                    <div class="landscape">
                        <div class="cloud cloud1"></div>
                    </div>
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">PABLO - ST-3504-GR</div>
                    <div class="product-dimensions">150x50x66 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <div class="product-card">
                <div class="product-image">
                    <div class="landscape">
                        <div class="cloud cloud1"></div>
                    </div>
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">SAVONA - ST-4826</div>
                    <div class="product-dimensions">160x55x70 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <!-- Third Row -->
            <div class="product-card">
                <div class="product-image">
                    <div class="landscape">
                        <div class="cloud cloud1"></div>
                    </div>
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">MARTA - ST-3502-WH</div>
                    <div class="product-dimensions">140x45x60 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <div class="product-card">
                <div class="product-image">
                    <div class="landscape">
                        <div class="cloud cloud1"></div>
                    </div>
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">MARTA - ST-3502-GR</div>
                    <div class="product-dimensions">140x45x60 cm</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>

            <div class="product-card">
                <div class="product-image">
                    <div class="landscape">
                        <div class="cloud cloud1"></div>
                    </div>
                </div>
                <div class="discount-badge">-15%</div>
                <div class="product-info">
                    <div class="product-name">SAVONA - ST-4827</div>
                    <div class="price-section">
                        <span class="old-price">109€</span>
                        <span class="new-price">93€</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            TOUS LES PRIX INDIQUÉS DANS CE FOLDER PROMOTIONNEL SONT HORS TVA
            <div class="footer-website">WWW.MEUBELCO.BE</div>
        </div>
    </div>
</body>
</html>