<?php include 'products_folder.php';

// Ürünleri 12'şer gruplara böl
$productsPerPage = 9;
$productChunks = array_chunk($products, $productsPerPage);

// Header ve Footer fonksiyonları
function renderHeader() {
    return '
    <div class="header">
        <div class="logo-container">
            <img src="stock/logo.png" alt="MEUBELCO" class="logo-image">
        </div>
        <div class="promotion-badge">
            <img src="stock/promo.png" alt="PROMOTION" class="promo-image">
        </div>
        <div class="validity-badge">
            VALABLE DU<br>
            15/06 AU<br>
            30/06/2025
        </div>
    </div>';
}

function renderFooter() {
    return '
    <div class="footer">
        TOUS LES PRIX INDIQUÉS DANS CE FOLDER PROMOTIONNEL SONT HORS TVA
        <div class="footer-website">WWW.MEUBELCO.BE</div>
    </div>';
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MEUBELCO - Promotion</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-image: url('stock/background.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            background-repeat: no-repeat;
            background-color: #e8e8e8;
            padding: 15px;
            font-size: 12px;
        }

        .page {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            background: rgba(232, 232, 232, 0.95);
            padding: 20px;
            padding-top: 80px; /* Header için boşluk */
            padding-bottom: 80px; /* Footer için boşluk */
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #e8e8e8;
            z-index: 1000;
            padding: 5px 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .logo-container {
            width: 120px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .promotion-badge {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .promo-image {
            max-height: 50px;
            object-fit: contain;
        }

        .validity-badge {
            background-color: #ffd700;
            color: #dc143c;
            padding: 8px 12px;
            font-weight: bold;
            font-size: 10px;
            text-align: center;
            border-radius: 0;
        }

        /* Main Layout - Grid for all products */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 15px;
        }

        /* Product Cards */
        .product-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid #e0e0e0;
            border-radius: 15px;
            padding: 15px;
            position: relative;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #dc143c 0%, #ffd700 50%, #dc143c 100%);
        }

        .products-grid .product-card {
            height: 280px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .products-grid .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        /* Product Images */
        .product-image {
            width: 100%;
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            margin-bottom: 12px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.15);
        }

        .products-grid .product-image {
            height: 160px;
        }

        .product-photo {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 8px;
        }

        /* Discount Badge */
        .discount-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #dc143c;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.25);
            border: 2px solid #fff;
            z-index: 10;
        }

        /* Product Info */
        .product-info {
            position: absolute;
            bottom: 15px;
            left: 15px;
            right: 15px;
            background: rgba(255, 255, 255, 0.95);
            padding: 12px;
            border-radius: 8px;
            backdrop-filter: blur(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .product-name {
            font-weight: bold;
            font-size: 14px;
            color: #333;
            margin-bottom: 6px;
            line-height: 1.2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
        }

        .product-dimensions {
            font-size: 11px;
            color: #666;
            margin-bottom: 8px;
            font-style: italic;
        }

        .price-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .old-price {
            text-decoration: line-through;
            color: #666;
            font-size: 16px;
            font-weight: bold;
        }

        .new-price {
            color: #dc143c;
            font-size: 18px;
            font-weight: bold;
        }

        /* Footer */
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #e8e8e8;
            text-align: center;
            font-size: 12px;
            color: #333;
            font-weight: bold;
            line-height: 0.7;
            padding: 5px 20px;

            z-index: 1000;
        }

        .footer-website {
            color: #333;
            margin-top: 5px;
            font-size: 12px;
        }

        /* Print Styles - Optimized for Chrome PDF */
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            @page {
                size: A4;
                margin: 0.5in;
            }

            body {
                padding: 0 !important;
                margin: 0 !important;
                background: white !important;
                background-image: url('stock/background.jpg') !important;
                background-size: cover !important;
                background-position: center !important;
                background-repeat: no-repeat !important;
                font-size: 11px !important;

            }

            .page {
                width: 100% !important;
                max-width: none !important;
                min-height: auto !important;
                margin: 0 !important;
                padding: 80px 20px 50px 20px !important;
                background: none !important;
                border-radius: 0 !important;
                box-shadow: none !important;
            }

            .header {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                width: 100% !important;
                background: #e8e8e8 !important;
                padding: 5px 20px !important;
                margin: 0 !important;
                z-index: 1000 !important;
                box-shadow: none !important;
                border-bottom: 2px solid #ccc !important;
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                height: 65px !important;
            }

            .footer {
                position: fixed !important;
                bottom: 0 !important;
                left: 0 !important;
                right: 0 !important;
                width: 100% !important;
                background: #e8e8e8 !important;
                padding: 10px 20px !important;
                margin: 0 !important;
                z-index: 1000 !important;
                box-shadow: none !important;
                border-top: 2px solid #ccc !important;
                text-align: center !important;
                font-size: 11px !important;
                color: #333 !important;
                font-weight: bold !important;
                line-height: 1.3 !important;
                height: 50px !important;
            }

            .products-grid {
                display: grid !important;
                grid-template-columns: repeat(3, 1fr) !important;
                gap: 12px !important;
                margin-bottom: 15px !important;
            }

            .product-card {
                background: white !important;
                border: 2px solid #e0e0e0 !important;
                border-radius: 10px !important;
                padding: 12px !important;
                position: relative !important;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
                height: 240px !important;
                page-break-inside: avoid !important;
            }

            .product-card::before {
                background: linear-gradient(90deg, #dc143c 0%, #ffd700 50%, #dc143c 100%) !important;
                height: 3px !important;
            }

            .product-image {
                height: 130px !important;
                border-radius: 8px !important;
                margin-bottom: 10px !important;
                box-shadow: 0 2px 6px rgba(0,0,0,0.1) !important;
            }

            .product-photo {
                width: 100% !important;
                height: 100% !important;
                object-fit: contain !important;
                border-radius: 6px !important;
            }

            .discount-badge {
                background: #ffd700 !important;
                color: #dc143c !important;
                border: 2px solid #dc143c !important;
                width: 40px !important;
                height: 40px !important;
                font-size: 12px !important;
                font-weight: bold !important;
            }

            .product-info {
                background: rgba(255, 255, 255, 0.95) !important;
                padding: 8px !important;
                border-radius: 6px !important;
                box-shadow: 0 1px 4px rgba(0,0,0,0.1) !important;
            }

            .product-name {
                font-size: 12px !important;
                font-weight: bold !important;
                color: #333 !important;
                margin-bottom: 4px !important;
                line-height: 1.2 !important;
            }

            .product-dimensions {
                font-size: 10px !important;
                color: #666 !important;
                margin-bottom: 6px !important;
                font-style: italic !important;
            }

            .old-price {
                font-size: 14px !important;
                color: #666 !important;
                font-weight: bold !important;
            }

            .new-price {
                font-size: 16px !important;
                color: #dc143c !important;
                font-weight: bold !important;
            }

            .promotion-badge {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            .promo-image {
                max-height: 40px !important;
                object-fit: contain !important;
            }

            .validity-badge {
                background-color: #ffd700 !important;
                color: #dc143c !important;
                font-size: 9px !important;
                padding: 6px 10px !important;
            }

            .logo-container {
                width: 100px !important;
                height: 50px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            .logo-image {
                max-width: 100% !important;
                max-height: 100% !important;
                object-fit: contain !important;
            }
        }
    </style>
</head>
<body>
<div class="page">
    <!-- Header -->
    <div class="header">
        <div class="logo-container">
            <img src="stock/logo.png" alt="MEUBELCO" class="logo-image">
        </div>

        <div class="promotion-badge">
            <img src="stock/promo.png" alt="PROMOTION" class="promo-image">
        </div>

        <div class="validity-badge">
            VALABLE DU<br>
            15/06 AU<br>
            30/06/2025
        </div>
    </div>

    <!-- Products Grid - All products in rows of 3 -->
    <div class="products-grid">
        <?php
        // Her sayfada maksimum 12 ürün göster
        $productsPerPage = 12;
        $currentPageProducts = array_slice($products, 0, $productsPerPage);

        foreach ($currentPageProducts as $index => $product):
            ?>
            <div class="product-card">
                <div class="product-image">
                    <img src="<?php echo $product['photo']; ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" class="product-photo">
                </div>
                <?php if ($product['discount'] > 0): ?>
                    <div class="discount-badge">-<?php echo $product['discount']; ?>%</div>
                <?php endif; ?>
                <div class="product-info">
                    <div class="product-name"><?php echo htmlspecialchars($product['name']); ?></div>
                    <div class="product-dimensions"><?php echo $product['dimensions']; ?></div>
                    <div class="price-section">
                        <?php if ($product['discount'] > 0): ?>
                            <span class="old-price"><?php echo $product['price']; ?>€</span>
                            <span class="new-price"><?php echo $product['promo_price']; ?>€</span>
                        <?php else: ?>
                            <span class="new-price"><?php echo $product['price']; ?>€</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>

        <?php
        // Kalan ürünler için ek sayfalar oluştur
        $remainingProducts = array_slice($products, $productsPerPage);
        $productChunks = array_chunk($remainingProducts, $productsPerPage);

        foreach ($productChunks as $chunkIndex => $chunk):
        ?>
    </div>

    <!-- Footer -->
    <div class="footer">
        TOUS LES PRIX INDIQUÉS DANS CE FOLDER PROMOTIONNEL SONT HORS TVA
        <div class="footer-website">WWW.MEUBELCO.BE</div>
    </div>
</div>

<!-- Yeni Sayfa -->
<div class="page" style="page-break-before: always;">
    <!-- Header -->
    <div class="header">
        <div class="logo-container">
            <img src="stock/logo.png" alt="MEUBELCO" class="logo-image">
        </div>

        <div class="promotion-badge">
            <img src="stock/promo.png" alt="PROMOTION" class="promo-image">
        </div>

        <div class="validity-badge">
            VALABLE DU<br>
            15/06 AU<br>
            30/06/2025
        </div>
    </div>

    <!-- Products Grid -->
    <div class="products-grid">
        <?php foreach ($chunk as $product): ?>
            <div class="product-card">
                <div class="product-image">
                    <img src="<?php echo $product['photo']; ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" class="product-photo">
                </div>
                <?php if ($product['discount'] > 0): ?>
                    <div class="discount-badge">-<?php echo $product['discount']; ?>%</div>
                <?php endif; ?>
                <div class="product-info">
                    <div class="product-name"><?php echo htmlspecialchars($product['name']); ?></div>
                    <div class="product-dimensions"><?php echo $product['dimensions']; ?></div>
                    <div class="price-section">
                        <?php if ($product['discount'] > 0): ?>
                            <span class="old-price"><?php echo $product['price']; ?>€</span>
                            <span class="new-price"><?php echo $product['promo_price']; ?>€</span>
                        <?php else: ?>
                            <span class="new-price"><?php echo $product['price']; ?>€</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
        <?php endforeach; ?>
    </div>

    <!-- Footer -->
    <div class="footer">
        TOUS LES PRIX INDIQUÉS DANS CE FOLDER PROMOTIONNEL SONT HORS TVA
        <div class="footer-website">WWW.MEUBELCO.BE</div>
    </div>
</div>
</body>
</html>
